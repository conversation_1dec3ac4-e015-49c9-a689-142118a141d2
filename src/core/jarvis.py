from typing import Optional

try:
    from ..models.base_model import BaseModel
    from ..utils.logger import get_logger
except ImportError:
    from models.base_model import BaseModel
    from utils.logger import get_logger

log = get_logger("jarvis.core")


class Jarvis:
    def __init__(self, model: BaseModel):
        self.model = model
        log.info(f"Initialized Jarvis with model: {model.name}")

        class EchoModel(BaseModel):
            def generate(self, prompt: str, max_tokens: int = 128, temperature: float = 0.0) -> str:
                return f"Echo: {prompt[:max_tokens]}"
        self._fallback = EchoModel("echo")

        # Load plugins
        try:
            from plugins.plugin_manager import PluginManager
            self.plugins = PluginManager(base_path="plugins.custom")
            self.plugins.load_all()
            log.info(f"Loaded plugins: {list(self.plugins.plugins.keys())}")
        except Exception as e:
            log.warning(f"Plugin system unavailable: {e}")
            self.plugins = None

    def generate(self, prompt: str, max_tokens: int = 128, temperature: float = 0.2) -> str:
        try:
            # pre-hook
            try:
                if self.plugins:
                    self.plugins.run_hook("on_message", prompt)
            except Exception:
                log.debug("Plugin pre-hook failed", exc_info=True)

            out = self.model.generate(prompt, max_tokens=max_tokens, temperature=temperature)
            if isinstance(out, str) and out.startswith("[GGUF load error]"):
                log.warning("GGUF generation failed; falling back to echo")
                out = self._fallback.generate(prompt, max_tokens=max_tokens, temperature=0.0)

            # post-hook
            try:
                if self.plugins:
                    self.plugins.run_hook("on_generate", prompt, out)
            except Exception:
                log.debug("Plugin post-hook failed", exc_info=True)

            return out
        except Exception as e:
            log.exception("Generation failed; falling back to echo")
            return self._fallback.generate(prompt, max_tokens=max_tokens, temperature=0.0)

    def set_model(self, model: BaseModel):
        self.model = model
        log.info(f"Model switched to: {model.name}")

