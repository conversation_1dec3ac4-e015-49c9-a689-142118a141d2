#!/usr/bin/env python3
"""
<PERSON> AI Assistant - Voice Interface
Advanced voice interface with speech recognition, text-to-speech, and conversation management.
"""

import time
import threading
import queue
import json
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from enum import Enum
try:
    from .speech_recognition import transcribe_from_mic, SpeechRecognizer
    from .text_to_speech import TextToSpeech, speak_text
    from ...core.jarvis import Jarvis
    from ...utils.logger import get_logger
    from ...utils.error_handler import capture_error, ErrorCategory
except ImportError:
    from interfaces.voice.speech_recognition import transcribe_from_mic, SpeechRecognizer
    from interfaces.voice.text_to_speech import TextToSpeech, speak_text
    from core.jarvis import <PERSON>
    from utils.logger import get_logger
    from utils.error_handler import capture_error, ErrorCategory

log = get_logger('jarvis.interfaces.voice')

class VoiceState(Enum):
    """Voice interface states"""
    IDLE = "idle"
    LISTENING = "listening"
    PROCESSING = "processing"
    SPEAKING = "speaking"
    ERROR = "error"

@dataclass
class VoiceConfig:
    """Voice interface configuration"""
    wake_word: str = "jarvis"
    language: str = "en-US"
    voice_id: str = "default"
    speech_rate: float = 1.0
    speech_volume: float = 0.8
    continuous_listening: bool = True
    auto_speak_responses: bool = True
    noise_threshold: float = 0.5
    timeout_seconds: float = 5.0
    max_conversation_length: int = 10

class VoiceInterface:
    """Advanced voice interface for Jarvis AI Assistant"""

    def __init__(self, app: Optional[Jarvis] = None, config: VoiceConfig = None):
        self.app = app
        self.config = config or VoiceConfig()
        self.state = VoiceState.IDLE
        self.is_running = False

        # Components
        self.speech_recognizer = None
        self.text_to_speech = None

        # Threading
        self.listen_thread = None
        self.speak_queue = queue.Queue()
        self.speak_thread = None

        # Conversation management
        self.conversation_history = []
        self.session_id = f"voice_session_{int(time.time())}"

        # Callbacks
        self.state_callbacks: List[Callable[[VoiceState], None]] = []
        self.message_callbacks: List[Callable[[str, str], None]] = []  # (user_input, ai_response)

        # Statistics
        self.total_interactions = 0
        self.successful_recognitions = 0
        self.failed_recognitions = 0
        self.start_time = None

        # Initialize components
        self._initialize_components()

    def _initialize_components(self):
        """Initialize speech recognition and text-to-speech components"""
        try:
            # Initialize speech recognizer
            self.speech_recognizer = SpeechRecognizer()

            # Initialize text-to-speech
            self.text_to_speech = TextToSpeech(
                voice_id=self.config.voice_id,
                rate=self.config.speech_rate,
                volume=self.config.speech_volume
            )

            log.info("Voice interface components initialized successfully")

        except Exception as e:
            capture_error(e, context={'component': 'voice_interface_init'}, category=ErrorCategory.SYSTEM)
            log.error(f"Failed to initialize voice components: {e}")

    def start(self):
        """Start the voice interface"""
        if self.is_running:
            log.warning("Voice interface already running")
            return

        try:
            self.is_running = True
            self.start_time = time.time()
            self._set_state(VoiceState.IDLE)

            # Start speaking thread
            self.speak_thread = threading.Thread(target=self._speak_worker, daemon=True)
            self.speak_thread.start()

            # Start listening thread if continuous listening is enabled
            if self.config.continuous_listening:
                self.listen_thread = threading.Thread(target=self._listen_worker, daemon=True)
                self.listen_thread.start()

            # Welcome message
            self.speak("Voice interface activated. How can I help you?")

            log.info("Voice interface started successfully")

        except Exception as e:
            capture_error(e, context={'action': 'start_voice_interface'}, category=ErrorCategory.SYSTEM)
            self.is_running = False

    def stop(self):
        """Stop the voice interface"""
        if not self.is_running:
            return

        self.is_running = False
        self._set_state(VoiceState.IDLE)

        # Stop threads
        if self.listen_thread and self.listen_thread.is_alive():
            self.listen_thread.join(timeout=2.0)

        if self.speak_thread and self.speak_thread.is_alive():
            self.speak_thread.join(timeout=2.0)

        log.info("Voice interface stopped")

    def _listen_worker(self):
        """Background worker for continuous listening"""
        log.info("Voice listening worker started")

        while self.is_running:
            try:
                if self.state == VoiceState.IDLE:
                    self._set_state(VoiceState.LISTENING)

                    # Listen for speech
                    spoken_text = self._listen_for_speech()

                    if spoken_text:
                        # Check for wake word if configured
                        if self.config.wake_word and not self._contains_wake_word(spoken_text):
                            continue

                        # Process the speech
                        self._process_speech(spoken_text)
                    else:
                        self._set_state(VoiceState.IDLE)

                else:
                    # Wait if not idle
                    time.sleep(0.1)

            except Exception as e:
                capture_error(e, context={'worker': 'listen_worker'}, category=ErrorCategory.SYSTEM)
                self._set_state(VoiceState.ERROR)
                time.sleep(1.0)  # Brief pause before retrying

        log.info("Voice listening worker stopped")

    def _speak_worker(self):
        """Background worker for text-to-speech"""
        log.info("Voice speaking worker started")

        while self.is_running:
            try:
                # Get next item from speak queue
                try:
                    text = self.speak_queue.get(timeout=1.0)
                except queue.Empty:
                    continue

                if text:
                    self._set_state(VoiceState.SPEAKING)

                    # Speak the text
                    if self.text_to_speech:
                        self.text_to_speech.speak(text)
                    else:
                        # Fallback to simple TTS
                        speak_text(text)

                    self._set_state(VoiceState.IDLE)

            except Exception as e:
                capture_error(e, context={'worker': 'speak_worker'}, category=ErrorCategory.SYSTEM)
                self._set_state(VoiceState.ERROR)

        log.info("Voice speaking worker stopped")

    def _listen_for_speech(self) -> Optional[str]:
        """Listen for speech input"""
        try:
            if self.speech_recognizer:
                text = self.speech_recognizer.listen()
            else:
                # Fallback to simple recognition
                text = transcribe_from_mic()

            if text:
                self.successful_recognitions += 1
                log.debug(f"Recognized speech: {text}")
            else:
                self.failed_recognitions += 1

            return text

        except Exception as e:
            self.failed_recognitions += 1
            capture_error(e, context={'action': 'speech_recognition'}, category=ErrorCategory.SYSTEM)
            return None

    def _contains_wake_word(self, text: str) -> bool:
        """Check if text contains wake word"""
        if not self.config.wake_word:
            return True

        return self.config.wake_word.lower() in text.lower()

    def _process_speech(self, spoken_text: str):
        """Process recognized speech"""
        try:
            self._set_state(VoiceState.PROCESSING)
            self.total_interactions += 1

            # Remove wake word from text
            if self.config.wake_word:
                spoken_text = spoken_text.replace(self.config.wake_word, "").strip()

            if not spoken_text:
                self.speak("I didn't catch that. Could you please repeat?")
                return

            log.info(f"Processing speech: {spoken_text}")

            # Generate response using Jarvis
            response = self.app.generate(spoken_text)

            # Add to conversation history
            self.conversation_history.append({
                'timestamp': time.time(),
                'user_input': spoken_text,
                'ai_response': response,
                'session_id': self.session_id
            })

            # Limit conversation history
            if len(self.conversation_history) > self.config.max_conversation_length:
                self.conversation_history.pop(0)

            # Notify callbacks
            for callback in self.message_callbacks:
                try:
                    callback(spoken_text, response)
                except Exception as e:
                    log.error(f"Message callback failed: {e}")

            # Speak response if auto-speak is enabled
            if self.config.auto_speak_responses:
                self.speak(response)
            else:
                print(f"Jarvis: {response}")

        except Exception as e:
            capture_error(e, context={'action': 'process_speech', 'input': spoken_text}, category=ErrorCategory.SYSTEM)
            self.speak("I'm sorry, I encountered an error processing your request.")

        finally:
            self._set_state(VoiceState.IDLE)

    def speak(self, text: str):
        """Add text to speaking queue"""
        if self.is_running and text:
            self.speak_queue.put(text)

    def _set_state(self, new_state: VoiceState):
        """Set voice interface state and notify callbacks"""
        if self.state != new_state:
            old_state = self.state
            self.state = new_state

            log.debug(f"Voice state changed: {old_state.value} -> {new_state.value}")

            # Notify state callbacks
            for callback in self.state_callbacks:
                try:
                    callback(new_state)
                except Exception as e:
                    log.error(f"State callback failed: {e}")

    def add_state_callback(self, callback: Callable[[VoiceState], None]):
        """Add state change callback"""
        self.state_callbacks.append(callback)

    def add_message_callback(self, callback: Callable[[str, str], None]):
        """Add message callback"""
        self.message_callbacks.append(callback)

    def get_stats(self) -> Dict[str, Any]:
        """Get voice interface statistics"""
        uptime = time.time() - self.start_time if self.start_time else 0

        return {
            'state': self.state.value,
            'is_running': self.is_running,
            'uptime': uptime,
            'total_interactions': self.total_interactions,
            'successful_recognitions': self.successful_recognitions,
            'failed_recognitions': self.failed_recognitions,
            'recognition_success_rate': (
                self.successful_recognitions / max(1, self.successful_recognitions + self.failed_recognitions) * 100
            ),
            'conversation_length': len(self.conversation_history),
            'session_id': self.session_id
        }

    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get conversation history"""
        return self.conversation_history.copy()

    def clear_conversation_history(self):
        """Clear conversation history"""
        self.conversation_history.clear()
        self.session_id = f"voice_session_{int(time.time())}"
        log.info("Conversation history cleared")

    def set_config(self, **kwargs):
        """Update configuration"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                log.info(f"Updated voice config: {key} = {value}")

    def process_single_input(self) -> Optional[str]:
        """Process single voice input (non-continuous mode)"""
        try:
            self._set_state(VoiceState.LISTENING)

            spoken_text = self._listen_for_speech()

            if spoken_text:
                self._process_speech(spoken_text)
                return spoken_text
            else:
                self.speak("No speech recognized. Please try again.")
                return None

        except Exception as e:
            capture_error(e, context={'action': 'single_input'}, category=ErrorCategory.SYSTEM)
            return None
        finally:
            self._set_state(VoiceState.IDLE)

def run(app: Jarvis, config: VoiceConfig = None):
    """Run voice interface (legacy function)"""
    voice_interface = VoiceInterface(app, config)

    try:
        if config and config.continuous_listening:
            # Continuous mode
            voice_interface.start()

            print("Voice interface started. Say 'exit' or 'quit' to stop.")

            while voice_interface.is_running:
                try:
                    # Check for exit commands in conversation
                    recent_inputs = [h['user_input'].lower() for h in voice_interface.conversation_history[-3:]]
                    if any(cmd in ' '.join(recent_inputs) for cmd in ['exit', 'quit', 'stop', 'goodbye']):
                        voice_interface.speak("Goodbye!")
                        break

                    time.sleep(1.0)

                except KeyboardInterrupt:
                    break

        else:
            # Single input mode
            result = voice_interface.process_single_input()
            if not result:
                print("No speech recognized.")

    finally:
        voice_interface.stop()

def create_voice_interface(app: Jarvis, **config_kwargs) -> VoiceInterface:
    """Factory function to create voice interface"""
    config = VoiceConfig(**config_kwargs)
    return VoiceInterface(app, config)

