2025-08-21 18:12:08 | [32mINFO[0m | jarvis.services.database | _initialize_schema:178  | Database schema initialized successfully
2025-08-21 18:12:08 | [32mINFO[0m | jarvis.services.database | _start_backup_thread:195  | Automatic backup thread started
2025-08-21 18:12:08 | [32mINFO[0m | jarvis.test          | <module>       :27   | Test message
2025-08-21 18:12:08 | [32mINFO[0m | jarvis.engine.memory | _start_consolidation_thread:531  | Memory consolidation thread started
2025-08-21 18:12:08 | DEBUG    | jarvis.engine.loader | ensure_runtime_dirs:73   | Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/data
2025-08-21 18:12:08 | DEBUG    | jarvis.engine.loader | ensure_runtime_dirs:73   | Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/logs
2025-08-21 18:12:08 | DEBUG    | jarvis.engine.loader | ensure_runtime_dirs:73   | Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/cache
2025-08-21 18:12:08 | DEBUG    | jarvis.engine.loader | ensure_runtime_dirs:73   | Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/temp
2025-08-21 18:12:08 | DEBUG    | jarvis.engine.loader | ensure_runtime_dirs:73   | Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/models
2025-08-21 18:12:08 | DEBUG    | jarvis.engine.loader | ensure_runtime_dirs:73   | Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/config
2025-08-21 18:12:08 | DEBUG    | jarvis.engine.loader | ensure_runtime_dirs:73   | Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/data/sessions
2025-08-21 18:12:08 | DEBUG    | jarvis.engine.loader | ensure_runtime_dirs:73   | Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/data/backups
2025-08-21 18:12:08 | DEBUG    | jarvis.engine.loader | ensure_runtime_dirs:73   | Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/data/exports
2025-08-21 18:12:08 | DEBUG    | jarvis.engine.loader | ensure_runtime_dirs:73   | Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/logs/archive
2025-08-21 18:12:08 | DEBUG    | jarvis.engine.loader | ensure_runtime_dirs:73   | Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/cache/models
2025-08-21 18:12:08 | DEBUG    | jarvis.engine.loader | ensure_runtime_dirs:73   | Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/cache/responses
2025-08-21 18:12:08 | [32mINFO[0m | jarvis.engine.loader | ensure_runtime_dirs:77   | Ensured 12 runtime directories
2025-08-21 18:12:09 | [32mINFO[0m | jarvis.web           | setup_static_files:161  | Static files mounted from: web/static
2025-08-21 18:12:09 | [32mINFO[0m | jarvis.web           | setup_static_files:177  | Templates loaded from: web/templates
2025-08-21 22:01:50 | [32mINFO[0m | jarvis.web           | setup_static_files:165  | Static files mounted from: web/static
2025-08-21 22:01:50 | [32mINFO[0m | jarvis.web           | setup_static_files:181  | Templates loaded from: web/templates
2025-08-21 22:03:28 | [32mINFO[0m | jarvis.web           | setup_static_files:165  | Static files mounted from: web/static
2025-08-21 22:03:28 | [32mINFO[0m | jarvis.web           | setup_static_files:181  | Templates loaded from: web/templates
2025-08-21 22:03:28 | [32mINFO[0m | jarvis.web           | start          :786  | Starting web interface on 127.0.0.1:8080
2025-08-21 22:03:28 | [32mINFO[0m | jarvis.web           | startup_event  :759  | Jarvis Web Interface starting up...
2025-08-21 22:03:37 | [31mERROR[0m | jarvis.web           | internal_error_handler:746  | Internal server error: No route exists for name "static" and params "filename".
2025-08-21 22:03:43 | [32mINFO[0m | jarvis.web           | shutdown_event :764  | Jarvis Web Interface shutting down...
2025-08-21 22:07:02 | [31mERROR[0m | jarvis.error         | _log_error     :184  | [ERR_1755832022_2344] SpeechRecognizer.__init__() got an unexpected keyword argument 'language' | Context: {"component": "voice_interface_init"}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/jarviscelviteaz/src/interfaces/voice/voice_interface.py", line 90, in _initialize_components
    self.speech_recognizer = SpeechRecognizer(
                             ^^^^^^^^^^^^^^^^^
TypeError: SpeechRecognizer.__init__() got an unexpected keyword argument 'language'
2025-08-21 22:07:02 | [31mERROR[0m | jarvis.interfaces.voice | _initialize_components:106  | Failed to initialize voice components: SpeechRecognizer.__init__() got an unexpected keyword argument 'language'
2025-08-21 22:07:02 | [32mINFO[0m | jarvis.interfaces.voice | _speak_worker  :189  | Voice speaking worker started
2025-08-21 22:07:02 | [32mINFO[0m | jarvis.interfaces.voice | start          :131  | Voice interface started successfully
2025-08-21 22:07:02 | DEBUG    | jarvis.interfaces.voice | _set_state     :308  | Voice state changed: idle -> speaking
2025-08-21 22:07:02 | [32mINFO[0m | jarvis.interfaces.voice | _listen_worker :156  | Voice listening worker started
2025-08-21 22:14:58 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)
2025-08-21 22:14:58 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)
2025-08-21 22:14:58 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)
2025-08-21 22:14:58 | [32mINFO[0m | jarvis.config        | reload_all     :240  | Configuration reloaded successfully
2025-08-21 22:14:58 | [32mINFO[0m | jarvis               | main           :32   | Starting Jarvis
2025-08-21 22:14:58 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:14:58 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: echo
2025-08-21 22:14:58 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:14:58 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: No module named 'plugins.core.plugin_manager'
2025-08-21 22:16:08 | [32mINFO[0m | jarvis.web           | setup_static_files:165  | Static files mounted from: web/static
2025-08-21 22:16:08 | [32mINFO[0m | jarvis.web           | setup_static_files:181  | Templates loaded from: web/templates
2025-08-21 22:16:08 | [32mINFO[0m | jarvis.web           | startup_event  :759  | Jarvis Web Interface starting up...
2025-08-21 22:16:18 | [32mINFO[0m | jarvis.web           | shutdown_event :764  | Jarvis Web Interface shutting down...
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.config        | reload_all     :240  | Configuration reloaded successfully
2025-08-21 22:16:51 | [32mINFO[0m | jarvis               | main           :32   | Starting Jarvis
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: echo
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:16:51 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: cannot import name 'validate' from 'plugins.validator.plugin_validator' (/home/<USER>/Desktop/jarviscelviteaz/plugins/validator/plugin_validator.py)
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.config        | reload_all     :240  | Configuration reloaded successfully
2025-08-21 22:16:51 | [32mINFO[0m | jarvis               | main           :32   | Starting Jarvis
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.web           | setup_static_files:165  | Static files mounted from: web/static
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.web           | setup_static_files:181  | Templates loaded from: web/templates
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.config        | reload_all     :240  | Configuration reloaded successfully
2025-08-21 22:16:51 | [32mINFO[0m | jarvis               | main           :32   | Starting Jarvis
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: echo
2025-08-21 22:16:51 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:16:51 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: cannot import name 'validate' from 'plugins.validator.plugin_validator' (/home/<USER>/Desktop/jarviscelviteaz/plugins/validator/plugin_validator.py)
2025-08-21 22:16:51 | [31mERROR[0m | jarvis               | main           :56   | Unknown interface: invalid_interface
2025-08-21 22:16:52 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)
2025-08-21 22:16:52 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)
2025-08-21 22:16:52 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)
2025-08-21 22:16:52 | [32mINFO[0m | jarvis.config        | reload_all     :240  | Configuration reloaded successfully
2025-08-21 22:16:52 | [32mINFO[0m | jarvis               | main           :32   | Starting Jarvis
2025-08-21 22:16:52 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:16:52 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: echo
2025-08-21 22:16:52 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:16:52 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: cannot import name 'validate' from 'plugins.validator.plugin_validator' (/home/<USER>/Desktop/jarviscelviteaz/plugins/validator/plugin_validator.py)
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.config        | reload_all     :240  | Configuration reloaded successfully
2025-08-21 22:16:54 | [32mINFO[0m | jarvis               | main           :32   | Starting Jarvis
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: echo
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:16:54 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: cannot import name 'validate' from 'plugins.validator.plugin_validator' (/home/<USER>/Desktop/jarviscelviteaz/plugins/validator/plugin_validator.py)
2025-08-21 22:16:54 | [31mERROR[0m | jarvis               | main           :56   | Unknown interface: web
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.config        | reload_all     :240  | Configuration reloaded successfully
2025-08-21 22:16:54 | [32mINFO[0m | jarvis               | main           :32   | Starting Jarvis
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: echo
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:16:54 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: cannot import name 'validate' from 'plugins.validator.plugin_validator' (/home/<USER>/Desktop/jarviscelviteaz/plugins/validator/plugin_validator.py)
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.config        | reload_all     :240  | Configuration reloaded successfully
2025-08-21 22:16:54 | [32mINFO[0m | jarvis               | main           :32   | Starting Jarvis
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: echo
2025-08-21 22:16:54 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:16:54 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: cannot import name 'validate' from 'plugins.validator.plugin_validator' (/home/<USER>/Desktop/jarviscelviteaz/plugins/validator/plugin_validator.py)
2025-08-21 22:39:29 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: config/app.yaml (format: yaml, priority: 1)
2025-08-21 22:39:29 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: config/models.yaml (format: yaml, priority: 2)
2025-08-21 22:39:29 | [32mINFO[0m | jarvis.config        | add_source     :129  | Added configuration source: config/logging.yaml (format: yaml, priority: 3)
2025-08-21 22:39:29 | [32mINFO[0m | jarvis.config        | reload_all     :240  | Configuration reloaded successfully
2025-08-21 22:39:29 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: simple_echo
2025-08-21 22:39:29 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: simple_echo
2025-08-21 22:39:29 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:39:29 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: No module named 'plugins.core.plugin_manager'
2025-08-21 22:44:46 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: openhermes-2.5-mistral-7b.Q4_K_M.gguf
2025-08-21 22:44:46 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: openhermes-2.5-mistral-7b.Q4_K_M.gguf
2025-08-21 22:44:46 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-21 22:44:46 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: No module named 'plugins.core.plugin_manager'
2025-08-22 00:07:57 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: openhermes-2.5-mistral-7b.Q4_K_M.gguf
2025-08-22 00:07:57 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: openhermes-2.5-mistral-7b.Q4_K_M.gguf
2025-08-22 00:07:57 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-22 00:07:57 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: No module named 'plugins.core.plugin_manager'
2025-08-22 00:07:57 | [32mINFO[0m | jarvis.web           | setup_static_files:165  | Static files mounted from: web/static
2025-08-22 00:07:57 | [32mINFO[0m | jarvis.web           | setup_static_files:181  | Templates loaded from: web/templates
2025-08-22 00:07:57 | [32mINFO[0m | jarvis.web           | start          :786  | Starting web interface on 127.0.0.1:8080
2025-08-22 00:07:57 | [32mINFO[0m | jarvis.web           | startup_event  :759  | Jarvis Web Interface starting up...
2025-08-22 00:08:11 | [31mERROR[0m | jarvis.web           | internal_error_handler:746  | Internal server error: No route exists for name "static" and params "filename".
2025-08-22 00:08:36 | [31mERROR[0m | jarvis.web           | internal_error_handler:746  | Internal server error: No route exists for name "static" and params "filename".
2025-08-22 00:08:48 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: openhermes-2.5-mistral-7b.Q4_K_M.gguf
2025-08-22 00:08:48 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: openhermes-2.5-mistral-7b.Q4_K_M.gguf
2025-08-22 00:08:48 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-22 00:08:48 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: No module named 'plugins.core.plugin_manager'
2025-08-22 00:08:48 | [31mERROR[0m | jarvis.error         | _log_error     :184  | [ERR_1755839328_5001] SpeechRecognizer.__init__() got an unexpected keyword argument 'language' | Context: {"component": "voice_interface_init"}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/jarviscelviteaz/src/interfaces/voice/voice_interface.py", line 90, in _initialize_components
    self.speech_recognizer = SpeechRecognizer(
                             ^^^^^^^^^^^^^^^^^
TypeError: SpeechRecognizer.__init__() got an unexpected keyword argument 'language'
2025-08-22 00:08:48 | [31mERROR[0m | jarvis.interfaces.voice | _initialize_components:106  | Failed to initialize voice components: SpeechRecognizer.__init__() got an unexpected keyword argument 'language'
2025-08-22 00:08:48 | [32mINFO[0m | jarvis.interfaces.voice | _speak_worker  :189  | Voice speaking worker started
2025-08-22 00:08:48 | [32mINFO[0m | jarvis.interfaces.voice | _listen_worker :156  | Voice listening worker started
2025-08-22 00:08:48 | DEBUG    | jarvis.interfaces.voice | _set_state     :308  | Voice state changed: idle -> speaking
2025-08-22 00:08:48 | [32mINFO[0m | jarvis.interfaces.voice | start          :131  | Voice interface started successfully
2025-08-22 00:09:34 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: openhermes-2.5-mistral-7b.Q4_K_M.gguf
2025-08-22 00:09:34 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: openhermes-2.5-mistral-7b.Q4_K_M.gguf
2025-08-22 00:09:34 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-22 00:09:34 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: No module named 'plugins.core.plugin_manager'
2025-08-22 00:10:17 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: openhermes-2.5-mistral-7b.Q4_K_M.gguf
2025-08-22 00:10:17 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: openhermes-2.5-mistral-7b.Q4_K_M.gguf
2025-08-22 00:10:17 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-22 00:10:17 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: No module named 'plugins.core.plugin_manager'
2025-08-22 00:10:18 | [31mERROR[0m | jarvis.interfaces.voice.speech_recognition | _initialize_components:85   | speech_recognition library not installed
2025-08-22 00:10:18 | [31mERROR[0m | jarvis.error         | _log_error     :184  | [ERR_1755839418_2800] No module named 'speech_recognition' | Context: {"component": "voice_interface_init"}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/jarviscelviteaz/src/interfaces/voice/voice_interface.py", line 90, in _initialize_components
    self.speech_recognizer = SpeechRecognizer()
                             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/jarviscelviteaz/src/interfaces/voice/speech_recognition.py", line 59, in __init__
    self._initialize_components()
  File "/home/<USER>/Desktop/jarviscelviteaz/src/interfaces/voice/speech_recognition.py", line 64, in _initialize_components
    import speech_recognition as sr
ModuleNotFoundError: No module named 'speech_recognition'
2025-08-22 00:10:18 | [31mERROR[0m | jarvis.interfaces.voice | _initialize_components:103  | Failed to initialize voice components: No module named 'speech_recognition'
2025-08-22 00:10:18 | [32mINFO[0m | jarvis.interfaces.voice | _speak_worker  :186  | Voice speaking worker started
2025-08-22 00:10:18 | [32mINFO[0m | jarvis.interfaces.voice | start          :128  | Voice interface started successfully
2025-08-22 00:10:18 | DEBUG    | jarvis.interfaces.voice | _set_state     :305  | Voice state changed: idle -> speaking
2025-08-22 00:11:44 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: openhermes-2.5-mistral-7b.Q4_K_M.gguf
2025-08-22 00:11:44 | [32mINFO[0m | jarvis.core          | __init__       :16   | Initialized Jarvis with model: openhermes-2.5-mistral-7b.Q4_K_M.gguf
2025-08-22 00:11:44 | [32mINFO[0m | jarvis.models.base   | __init__       :107  | Initialized model: echo
2025-08-22 00:11:44 | [33mWARNING[0m | jarvis.core          | __init__       :30   | Plugin system unavailable: No module named 'plugins.core.plugin_manager'
2025-08-22 00:11:45 | [32mINFO[0m | jarvis.interfaces.voice.speech_recognition | _initialize_components:79   | Adjusting for ambient noise...
2025-08-22 00:11:46 | [32mINFO[0m | jarvis.interfaces.voice.speech_recognition | _initialize_components:82   | Speech recognition initialized with google engine
2025-08-22 00:11:46 | [31mERROR[0m | jarvis.error         | _log_error     :184  | [ERR_1755839506_5398] TextToSpeech.__init__() got an unexpected keyword argument 'voice_id' | Context: {"component": "voice_interface_init"}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/jarviscelviteaz/src/interfaces/voice/voice_interface.py", line 93, in _initialize_components
    self.text_to_speech = TextToSpeech(
                          ^^^^^^^^^^^^^
TypeError: TextToSpeech.__init__() got an unexpected keyword argument 'voice_id'
2025-08-22 00:11:46 | [31mERROR[0m | jarvis.interfaces.voice | _initialize_components:103  | Failed to initialize voice components: TextToSpeech.__init__() got an unexpected keyword argument 'voice_id'
2025-08-22 00:11:46 | [32mINFO[0m | jarvis.interfaces.voice | _speak_worker  :186  | Voice speaking worker started
2025-08-22 00:11:46 | [32mINFO[0m | jarvis.interfaces.voice | _listen_worker :153  | Voice listening worker started
2025-08-22 00:11:46 | DEBUG    | jarvis.interfaces.voice | _set_state     :305  | Voice state changed: idle -> speaking
2025-08-22 00:11:46 | [32mINFO[0m | jarvis.interfaces.voice | start          :128  | Voice interface started successfully
2025-08-22 00:12:10 | [31mERROR[0m | jarvis.web           | internal_error_handler:746  | Internal server error: No route exists for name "static" and params "filename".
