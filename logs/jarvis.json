{"timestamp": "2025-08-21T18:12:08.844406", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.services.database", "message": "Database schema initialized successfully", "module": "database_service", "function": "_initialize_schema", "line": 178, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "uptime": 0.04471421241760254, "memory_usage": 18.16, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.856924", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.services.database", "message": "Automatic backup thread started", "module": "database_service", "function": "_start_backup_thread", "line": 195, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "uptime": 0.05720710754394531, "memory_usage": 18.16, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.857156", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.test", "message": "Test message", "module": "<string>", "function": "<module>", "line": 27, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "uptime": 0.05742764472961426, "memory_usage": 18.16, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.867592", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.engine.memory", "message": "Memory consolidation thread started", "module": "memory", "function": "_start_consolidation_thread", "line": 531, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "uptime": 0.06787467002868652, "memory_usage": 23.05, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.887395", "level": "DEBUG", "logger": "jarvis.engine.loader", "message": "Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/data", "module": "loader", "function": "ensure_runtime_dirs", "line": 73, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.887632", "level": "DEBUG", "logger": "jarvis.engine.loader", "message": "Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/logs", "module": "loader", "function": "ensure_runtime_dirs", "line": 73, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.887769", "level": "DEBUG", "logger": "jarvis.engine.loader", "message": "Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/cache", "module": "loader", "function": "ensure_runtime_dirs", "line": 73, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.887861", "level": "DEBUG", "logger": "jarvis.engine.loader", "message": "Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/temp", "module": "loader", "function": "ensure_runtime_dirs", "line": 73, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.887942", "level": "DEBUG", "logger": "jarvis.engine.loader", "message": "Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/models", "module": "loader", "function": "ensure_runtime_dirs", "line": 73, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.888031", "level": "DEBUG", "logger": "jarvis.engine.loader", "message": "Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/config", "module": "loader", "function": "ensure_runtime_dirs", "line": 73, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.888114", "level": "DEBUG", "logger": "jarvis.engine.loader", "message": "Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/data/sessions", "module": "loader", "function": "ensure_runtime_dirs", "line": 73, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.888206", "level": "DEBUG", "logger": "jarvis.engine.loader", "message": "Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/data/backups", "module": "loader", "function": "ensure_runtime_dirs", "line": 73, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.888294", "level": "DEBUG", "logger": "jarvis.engine.loader", "message": "Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/data/exports", "module": "loader", "function": "ensure_runtime_dirs", "line": 73, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.888379", "level": "DEBUG", "logger": "jarvis.engine.loader", "message": "Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/logs/archive", "module": "loader", "function": "ensure_runtime_dirs", "line": 73, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.888463", "level": "DEBUG", "logger": "jarvis.engine.loader", "message": "Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/cache/models", "module": "loader", "function": "ensure_runtime_dirs", "line": 73, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.888548", "level": "DEBUG", "logger": "jarvis.engine.loader", "message": "Ensured dir: /home/<USER>/Desktop/jarviscelviteaz/src/cache/responses", "module": "loader", "function": "ensure_runtime_dirs", "line": 73, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:08.888613", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.engine.loader", "message": "Ensured 12 runtime directories", "module": "loader", "function": "ensure_runtime_dirs", "line": 77, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "uptime": 0.08888125419616699, "memory_usage": 23.67, "asctime": "2025-08-21 18:12:08"}
{"timestamp": "2025-08-21T18:12:09.263593", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Static files mounted from: web/static", "module": "web_interface", "function": "setup_static_files", "line": 161, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "uptime": 0.46387481689453125, "memory_usage": 46.23, "asctime": "2025-08-21 18:12:09"}
{"timestamp": "2025-08-21T18:12:09.264054", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Templates loaded from: web/templates", "module": "web_interface", "function": "setup_static_files", "line": 177, "thread": 127166194421888, "thread_name": "MainThread", "process": 422700, "taskName": null, "uptime": 0.46434926986694336, "memory_usage": 46.23, "asctime": "2025-08-21 18:12:09"}
{"timestamp": "2025-08-21T22:01:50.864379", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Static files mounted from: web/static", "module": "web_interface", "function": "setup_static_files", "line": 165, "thread": 132630893621376, "thread_name": "MainThread", "process": 438376, "taskName": null, "uptime": 0.005952596664428711, "memory_usage": 44.46, "asctime": "2025-08-21 22:01:50"}
{"timestamp": "2025-08-21T22:01:50.872183", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Templates loaded from: web/templates", "module": "web_interface", "function": "setup_static_files", "line": 181, "thread": 132630893621376, "thread_name": "MainThread", "process": 438376, "taskName": null, "uptime": 0.013752937316894531, "memory_usage": 44.46, "asctime": "2025-08-21 22:01:50"}
{"timestamp": "2025-08-21T22:03:28.556614", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Static files mounted from: web/static", "module": "web_interface", "function": "setup_static_files", "line": 165, "thread": 135877902962816, "thread_name": "MainThread", "process": 438450, "taskName": null, "uptime": 0.003489255905151367, "memory_usage": 43.98, "asctime": "2025-08-21 22:03:28"}
{"timestamp": "2025-08-21T22:03:28.564910", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Templates loaded from: web/templates", "module": "web_interface", "function": "setup_static_files", "line": 181, "thread": 135877902962816, "thread_name": "MainThread", "process": 438450, "taskName": null, "uptime": 0.011770963668823242, "memory_usage": 43.98, "asctime": "2025-08-21 22:03:28"}
{"timestamp": "2025-08-21T22:03:28.584519", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Starting web interface on 127.0.0.1:8080", "module": "web_interface", "function": "start", "line": 786, "thread": 135877902962816, "thread_name": "MainThread", "process": 438450, "taskName": null, "uptime": 0.03139162063598633, "memory_usage": 46.39, "asctime": "2025-08-21 22:03:28"}
{"timestamp": "2025-08-21T22:03:28.601461", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Jarvis Web Interface starting up...", "module": "web_interface", "function": "startup_event", "line": 759, "thread": 135877902962816, "thread_name": "MainThread", "process": 438450, "taskName": "Task-2", "uptime": 0.04831433296203613, "memory_usage": 47.64, "asctime": "2025-08-21 22:03:28"}
{"timestamp": "2025-08-21T22:03:37.356972", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis.web", "message": "Internal server error: No route exists for name \"static\" and params \"filename\".", "module": "web_interface", "function": "internal_error_handler", "line": 746, "thread": 135877902962816, "thread_name": "MainThread", "process": 438450, "taskName": "Task-5", "uptime": 8.80386996269226, "memory_usage": 48.45, "asctime": "2025-08-21 22:03:37"}
{"timestamp": "2025-08-21T22:03:43.440723", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Jarvis Web Interface shutting down...", "module": "web_interface", "function": "shutdown_event", "line": 764, "thread": 135877902962816, "thread_name": "MainThread", "process": 438450, "taskName": "Task-2", "uptime": 14.887572765350342, "memory_usage": 49.45, "asctime": "2025-08-21 22:03:43"}
{"timestamp": "2025-08-21T22:07:02.944702", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis.error", "message": "[ERR_1755832022_2344] SpeechRecognizer.__init__() got an unexpected keyword argument 'language' | Context: {\"component\": \"voice_interface_init\"}", "module": "error_handler", "function": "_log_error", "line": 184, "thread": 132706198560896, "thread_name": "MainThread", "process": 438943, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/jarviscelviteaz/src/interfaces/voice/voice_interface.py\", line 90, in _initialize_components\n    self.speech_recognizer = SpeechRecognizer(\n                             ^^^^^^^^^^^^^^^^^\nTypeError: SpeechRecognizer.__init__() got an unexpected keyword argument 'language'", "taskName": null, "uptime": 0.005670785903930664, "memory_usage": 42.44, "asctime": "2025-08-21 22:07:02"}
{"timestamp": "2025-08-21T22:07:02.950787", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Failed to initialize voice components: SpeechRecognizer.__init__() got an unexpected keyword argument 'language'", "module": "voice_interface", "function": "_initialize_components", "line": 106, "thread": 132706198560896, "thread_name": "MainThread", "process": 438943, "taskName": null, "uptime": 0.011748075485229492, "memory_usage": 42.44, "asctime": "2025-08-21 22:07:02"}
{"timestamp": "2025-08-21T22:07:02.951187", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Voice speaking worker started", "module": "voice_interface", "function": "_speak_worker", "line": 189, "thread": 132706165847744, "thread_name": "Thread-1 (_speak_worker)", "process": 438943, "taskName": null, "uptime": 0.01214456558227539, "memory_usage": 42.56, "asctime": "2025-08-21 22:07:02"}
{"timestamp": "2025-08-21T22:07:02.951356", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Voice interface started successfully", "module": "voice_interface", "function": "start", "line": 131, "thread": 132706198560896, "thread_name": "MainThread", "process": 438943, "taskName": null, "uptime": 0.01230764389038086, "memory_usage": 42.56, "asctime": "2025-08-21 22:07:02"}
{"timestamp": "2025-08-21T22:07:02.951772", "level": "DEBUG", "logger": "jarvis.interfaces.voice", "message": "Voice state changed: idle -> speaking", "module": "voice_interface", "function": "_set_state", "line": 308, "thread": 132706165847744, "thread_name": "Thread-1 (_speak_worker)", "process": 438943, "taskName": null, "asctime": "2025-08-21 22:07:02"}
{"timestamp": "2025-08-21T22:14:58.581319", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 135814903726208, "thread_name": "MainThread", "process": 440082, "taskName": null, "uptime": 0.007645606994628906, "memory_usage": 24.25, "asctime": "2025-08-21 22:14:58"}
{"timestamp": "2025-08-21T22:14:58.590518", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 135814903726208, "thread_name": "MainThread", "process": 440082, "taskName": null, "uptime": 0.016822338104248047, "memory_usage": 24.25, "asctime": "2025-08-21 22:14:58"}
{"timestamp": "2025-08-21T22:14:58.590904", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 135814903726208, "thread_name": "MainThread", "process": 440082, "taskName": null, "uptime": 0.01719379425048828, "memory_usage": 24.25, "asctime": "2025-08-21 22:14:58"}
{"timestamp": "2025-08-21T22:14:58.591247", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Configuration reloaded successfully", "module": "config_loader", "function": "reload_all", "line": 240, "thread": 135814903726208, "thread_name": "MainThread", "process": 440082, "taskName": null, "uptime": 0.017536163330078125, "memory_usage": 24.25, "asctime": "2025-08-21 22:14:58"}
{"timestamp": "2025-08-21T22:14:58.591982", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis", "message": "Starting Jarvis", "module": "main", "function": "main", "line": 32, "thread": 135814903726208, "thread_name": "MainThread", "process": 440082, "taskName": null, "uptime": 6.389617919921875e-05, "memory_usage": 24.25, "asctime": "2025-08-21 22:14:58"}
{"timestamp": "2025-08-21T22:14:58.592279", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 135814903726208, "thread_name": "MainThread", "process": 440082, "taskName": null, "uptime": 0.00035953521728515625, "memory_usage": 24.25, "asctime": "2025-08-21 22:14:58"}
{"timestamp": "2025-08-21T22:14:58.592486", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: echo", "module": "jarvis", "function": "__init__", "line": 16, "thread": 135814903726208, "thread_name": "MainThread", "process": 440082, "taskName": null, "uptime": 0.0005657672882080078, "memory_usage": 24.25, "asctime": "2025-08-21 22:14:58"}
{"timestamp": "2025-08-21T22:14:58.592706", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 135814903726208, "thread_name": "MainThread", "process": 440082, "taskName": null, "uptime": 0.0007863044738769531, "memory_usage": 24.25, "asctime": "2025-08-21 22:14:58"}
{"timestamp": "2025-08-21T22:14:58.598885", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: No module named 'plugins.core.plugin_manager'", "module": "jarvis", "function": "__init__", "line": 30, "thread": 135814903726208, "thread_name": "MainThread", "process": 440082, "taskName": null, "uptime": 0.006975412368774414, "memory_usage": 25.12, "asctime": "2025-08-21 22:14:58"}
{"timestamp": "2025-08-21T22:16:08.195164", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Static files mounted from: web/static", "module": "web_interface", "function": "setup_static_files", "line": 165, "thread": 140173664309376, "thread_name": "MainThread", "process": 440250, "taskName": null, "uptime": 0.018944501876831055, "memory_usage": 43.26, "asctime": "2025-08-21 22:16:08"}
{"timestamp": "2025-08-21T22:16:08.201529", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Templates loaded from: web/templates", "module": "web_interface", "function": "setup_static_files", "line": 181, "thread": 140173664309376, "thread_name": "MainThread", "process": 440250, "taskName": null, "uptime": 0.0252988338470459, "memory_usage": 43.26, "asctime": "2025-08-21 22:16:08"}
{"timestamp": "2025-08-21T22:16:08.255862", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Jarvis Web Interface starting up...", "module": "web_interface", "function": "startup_event", "line": 759, "thread": 140173664309376, "thread_name": "MainThread", "process": 440250, "taskName": "Task-2", "uptime": 0.07961869239807129, "memory_usage": 46.91, "asctime": "2025-08-21 22:16:08"}
{"timestamp": "2025-08-21T22:16:18.079372", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Jarvis Web Interface shutting down...", "module": "web_interface", "function": "shutdown_event", "line": 764, "thread": 140173664309376, "thread_name": "MainThread", "process": 440250, "taskName": "Task-2", "uptime": 9.903126955032349, "memory_usage": 47.04, "asctime": "2025-08-21 22:16:18"}
{"timestamp": "2025-08-21T22:16:51.140883", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 133123737800832, "thread_name": "MainThread", "process": 440363, "taskName": null, "uptime": 0.004973411560058594, "memory_usage": 24.2, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.146741", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 133123737800832, "thread_name": "MainThread", "process": 440363, "taskName": null, "uptime": 0.01082301139831543, "memory_usage": 24.2, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.146962", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 133123737800832, "thread_name": "MainThread", "process": 440363, "taskName": null, "uptime": 0.011032581329345703, "memory_usage": 24.2, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.147180", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Configuration reloaded successfully", "module": "config_loader", "function": "reload_all", "line": 240, "thread": 133123737800832, "thread_name": "MainThread", "process": 440363, "taskName": null, "uptime": 0.01125025749206543, "memory_usage": 24.2, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.147631", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis", "message": "Starting Jarvis", "module": "main", "function": "main", "line": 32, "thread": 133123737800832, "thread_name": "MainThread", "process": 440363, "taskName": null, "uptime": 3.910064697265625e-05, "memory_usage": 24.2, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.147813", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 133123737800832, "thread_name": "MainThread", "process": 440363, "taskName": null, "uptime": 0.0002224445343017578, "memory_usage": 24.2, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.147955", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: echo", "module": "jarvis", "function": "__init__", "line": 16, "thread": 133123737800832, "thread_name": "MainThread", "process": 440363, "taskName": null, "uptime": 0.0003619194030761719, "memory_usage": 24.2, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.148094", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 133123737800832, "thread_name": "MainThread", "process": 440363, "taskName": null, "uptime": 0.0005011558532714844, "memory_usage": 24.2, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.151664", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: cannot import name 'validate' from 'plugins.validator.plugin_validator' (/home/<USER>/Desktop/jarviscelviteaz/plugins/validator/plugin_validator.py)", "module": "jarvis", "function": "__init__", "line": 30, "thread": 133123737800832, "thread_name": "MainThread", "process": 440363, "taskName": null, "uptime": 9.608268737792969e-05, "memory_usage": 25.07, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.211343", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 135762543255680, "thread_name": "MainThread", "process": 440361, "taskName": null, "uptime": 0.0032455921173095703, "memory_usage": 40.32, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.217374", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 135762543255680, "thread_name": "MainThread", "process": 440361, "taskName": null, "uptime": 0.009258270263671875, "memory_usage": 40.32, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.217546", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 135762543255680, "thread_name": "MainThread", "process": 440361, "taskName": null, "uptime": 0.009420156478881836, "memory_usage": 40.32, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.217730", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Configuration reloaded successfully", "module": "config_loader", "function": "reload_all", "line": 240, "thread": 135762543255680, "thread_name": "MainThread", "process": 440361, "taskName": null, "uptime": 0.009604215621948242, "memory_usage": 40.32, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.218149", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis", "message": "Starting Jarvis", "module": "main", "function": "main", "line": 32, "thread": 135762543255680, "thread_name": "MainThread", "process": 440361, "taskName": null, "uptime": 3.600120544433594e-05, "memory_usage": 40.32, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.454321", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Static files mounted from: web/static", "module": "web_interface", "function": "setup_static_files", "line": 165, "thread": 135762543255680, "thread_name": "MainThread", "process": 440361, "taskName": null, "uptime": 0.23621845245361328, "memory_usage": 56.62, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.454633", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Templates loaded from: web/templates", "module": "web_interface", "function": "setup_static_files", "line": 181, "thread": 135762543255680, "thread_name": "MainThread", "process": 440361, "taskName": null, "uptime": 0.2365250587463379, "memory_usage": 56.62, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.636102", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 140097039745152, "thread_name": "MainThread", "process": 440365, "taskName": null, "uptime": 0.004718780517578125, "memory_usage": 24.23, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.641923", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 140097039745152, "thread_name": "MainThread", "process": 440365, "taskName": null, "uptime": 0.010528087615966797, "memory_usage": 24.23, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.642140", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 140097039745152, "thread_name": "MainThread", "process": 440365, "taskName": null, "uptime": 0.010738372802734375, "memory_usage": 24.23, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.642345", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Configuration reloaded successfully", "module": "config_loader", "function": "reload_all", "line": 240, "thread": 140097039745152, "thread_name": "MainThread", "process": 440365, "taskName": null, "uptime": 0.010942459106445312, "memory_usage": 24.23, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.642814", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis", "message": "Starting Jarvis", "module": "main", "function": "main", "line": 32, "thread": 140097039745152, "thread_name": "MainThread", "process": 440365, "taskName": null, "uptime": 4.553794860839844e-05, "memory_usage": 24.23, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.643022", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 140097039745152, "thread_name": "MainThread", "process": 440365, "taskName": null, "uptime": 0.00025272369384765625, "memory_usage": 24.23, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.643168", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: echo", "module": "jarvis", "function": "__init__", "line": 16, "thread": 140097039745152, "thread_name": "MainThread", "process": 440365, "taskName": null, "uptime": 0.0004076957702636719, "memory_usage": 24.23, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.643368", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 140097039745152, "thread_name": "MainThread", "process": 440365, "taskName": null, "uptime": 0.0005981922149658203, "memory_usage": 24.23, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.645266", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: cannot import name 'validate' from 'plugins.validator.plugin_validator' (/home/<USER>/Desktop/jarviscelviteaz/plugins/validator/plugin_validator.py)", "module": "jarvis", "function": "__init__", "line": 30, "thread": 140097039745152, "thread_name": "MainThread", "process": 440365, "taskName": null, "uptime": 8.988380432128906e-05, "memory_usage": 24.36, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:51.645458", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis", "message": "Unknown interface: invalid_interface", "module": "main", "function": "main", "line": 56, "thread": 140097039745152, "thread_name": "MainThread", "process": 440365, "taskName": null, "uptime": 0.0002770423889160156, "memory_usage": 24.36, "asctime": "2025-08-21 22:16:51"}
{"timestamp": "2025-08-21T22:16:52.019371", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 132979467026560, "thread_name": "MainThread", "process": 440381, "taskName": null, "uptime": 0.004631757736206055, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:52"}
{"timestamp": "2025-08-21T22:16:52.024962", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 132979467026560, "thread_name": "MainThread", "process": 440381, "taskName": null, "uptime": 0.010209321975708008, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:52"}
{"timestamp": "2025-08-21T22:16:52.025153", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 132979467026560, "thread_name": "MainThread", "process": 440381, "taskName": null, "uptime": 0.01039576530456543, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:52"}
{"timestamp": "2025-08-21T22:16:52.025409", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Configuration reloaded successfully", "module": "config_loader", "function": "reload_all", "line": 240, "thread": 132979467026560, "thread_name": "MainThread", "process": 440381, "taskName": null, "uptime": 0.010652303695678711, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:52"}
{"timestamp": "2025-08-21T22:16:52.025878", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis", "message": "Starting Jarvis", "module": "main", "function": "main", "line": 32, "thread": 132979467026560, "thread_name": "MainThread", "process": 440381, "taskName": null, "uptime": 4.0531158447265625e-05, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:52"}
{"timestamp": "2025-08-21T22:16:52.026046", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 132979467026560, "thread_name": "MainThread", "process": 440381, "taskName": null, "uptime": 0.0002071857452392578, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:52"}
{"timestamp": "2025-08-21T22:16:52.026170", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: echo", "module": "jarvis", "function": "__init__", "line": 16, "thread": 132979467026560, "thread_name": "MainThread", "process": 440381, "taskName": null, "uptime": 0.0003304481506347656, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:52"}
{"timestamp": "2025-08-21T22:16:52.026308", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 132979467026560, "thread_name": "MainThread", "process": 440381, "taskName": null, "uptime": 0.00046896934509277344, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:52"}
{"timestamp": "2025-08-21T22:16:52.028018", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: cannot import name 'validate' from 'plugins.validator.plugin_validator' (/home/<USER>/Desktop/jarviscelviteaz/plugins/validator/plugin_validator.py)", "module": "jarvis", "function": "__init__", "line": 30, "thread": 132979467026560, "thread_name": "MainThread", "process": 440381, "taskName": null, "uptime": 5.650520324707031e-05, "memory_usage": 24.41, "asctime": "2025-08-21 22:16:52"}
{"timestamp": "2025-08-21T22:16:54.101636", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 128691211366528, "thread_name": "MainThread", "process": 440391, "taskName": null, "uptime": 0.004558563232421875, "memory_usage": 24.31, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.107285", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 128691211366528, "thread_name": "MainThread", "process": 440391, "taskName": null, "uptime": 0.010180234909057617, "memory_usage": 24.31, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.107473", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 128691211366528, "thread_name": "MainThread", "process": 440391, "taskName": null, "uptime": 0.010362625122070312, "memory_usage": 24.31, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.107699", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Configuration reloaded successfully", "module": "config_loader", "function": "reload_all", "line": 240, "thread": 128691211366528, "thread_name": "MainThread", "process": 440391, "taskName": null, "uptime": 0.010587453842163086, "memory_usage": 24.31, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.108108", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis", "message": "Starting Jarvis", "module": "main", "function": "main", "line": 32, "thread": 128691211366528, "thread_name": "MainThread", "process": 440391, "taskName": null, "uptime": 3.528594970703125e-05, "memory_usage": 24.31, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.108278", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 128691211366528, "thread_name": "MainThread", "process": 440391, "taskName": null, "uptime": 0.00020599365234375, "memory_usage": 24.31, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.108407", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: echo", "module": "jarvis", "function": "__init__", "line": 16, "thread": 128691211366528, "thread_name": "MainThread", "process": 440391, "taskName": null, "uptime": 0.0003345012664794922, "memory_usage": 24.31, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.108566", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 128691211366528, "thread_name": "MainThread", "process": 440391, "taskName": null, "uptime": 0.0004932880401611328, "memory_usage": 24.31, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.110357", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: cannot import name 'validate' from 'plugins.validator.plugin_validator' (/home/<USER>/Desktop/jarviscelviteaz/plugins/validator/plugin_validator.py)", "module": "jarvis", "function": "__init__", "line": 30, "thread": 128691211366528, "thread_name": "MainThread", "process": 440391, "taskName": null, "uptime": 5.817413330078125e-05, "memory_usage": 24.43, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.110528", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis", "message": "Unknown interface: web", "module": "main", "function": "main", "line": 56, "thread": 128691211366528, "thread_name": "MainThread", "process": 440391, "taskName": null, "uptime": 0.0002288818359375, "memory_usage": 24.43, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.187727", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 137577952301184, "thread_name": "MainThread", "process": 440392, "taskName": null, "uptime": 0.005660533905029297, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.193814", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 137577952301184, "thread_name": "MainThread", "process": 440392, "taskName": null, "uptime": 0.011724233627319336, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.194044", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 137577952301184, "thread_name": "MainThread", "process": 440392, "taskName": null, "uptime": 0.011947154998779297, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.194263", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Configuration reloaded successfully", "module": "config_loader", "function": "reload_all", "line": 240, "thread": 137577952301184, "thread_name": "MainThread", "process": 440392, "taskName": null, "uptime": 0.012166261672973633, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.194707", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis", "message": "Starting Jarvis", "module": "main", "function": "main", "line": 32, "thread": 137577952301184, "thread_name": "MainThread", "process": 440392, "taskName": null, "uptime": 3.886222839355469e-05, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.194890", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 137577952301184, "thread_name": "MainThread", "process": 440392, "taskName": null, "uptime": 0.00022172927856445312, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.195039", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: echo", "module": "jarvis", "function": "__init__", "line": 16, "thread": 137577952301184, "thread_name": "MainThread", "process": 440392, "taskName": null, "uptime": 0.0003693103790283203, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.195186", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 137577952301184, "thread_name": "MainThread", "process": 440392, "taskName": null, "uptime": 0.0005180835723876953, "memory_usage": 24.29, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.196891", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: cannot import name 'validate' from 'plugins.validator.plugin_validator' (/home/<USER>/Desktop/jarviscelviteaz/plugins/validator/plugin_validator.py)", "module": "jarvis", "function": "__init__", "line": 30, "thread": 137577952301184, "thread_name": "MainThread", "process": 440392, "taskName": null, "uptime": 7.724761962890625e-05, "memory_usage": 24.42, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.273087", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/app.yaml (format: yaml, priority: 1)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 131814122619008, "thread_name": "MainThread", "process": 440393, "taskName": null, "uptime": 0.004970073699951172, "memory_usage": 24.02, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.278985", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/models.yaml (format: yaml, priority: 2)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 131814122619008, "thread_name": "MainThread", "process": 440393, "taskName": null, "uptime": 0.010852813720703125, "memory_usage": 24.02, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.279185", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: /home/<USER>/Desktop/jarviscelviteaz/config/logging.yaml (format: yaml, priority: 3)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 131814122619008, "thread_name": "MainThread", "process": 440393, "taskName": null, "uptime": 0.01104736328125, "memory_usage": 24.02, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.279399", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Configuration reloaded successfully", "module": "config_loader", "function": "reload_all", "line": 240, "thread": 131814122619008, "thread_name": "MainThread", "process": 440393, "taskName": null, "uptime": 0.011261463165283203, "memory_usage": 24.02, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.279864", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis", "message": "Starting Jarvis", "module": "main", "function": "main", "line": 32, "thread": 131814122619008, "thread_name": "MainThread", "process": 440393, "taskName": null, "uptime": 4.029273986816406e-05, "memory_usage": 24.02, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.280051", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 131814122619008, "thread_name": "MainThread", "process": 440393, "taskName": null, "uptime": 0.00022745132446289062, "memory_usage": 24.02, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.280184", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: echo", "module": "jarvis", "function": "__init__", "line": 16, "thread": 131814122619008, "thread_name": "MainThread", "process": 440393, "taskName": null, "uptime": 0.0003609657287597656, "memory_usage": 24.02, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.280341", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 131814122619008, "thread_name": "MainThread", "process": 440393, "taskName": null, "uptime": 0.0005161762237548828, "memory_usage": 24.02, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:16:54.281885", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: cannot import name 'validate' from 'plugins.validator.plugin_validator' (/home/<USER>/Desktop/jarviscelviteaz/plugins/validator/plugin_validator.py)", "module": "jarvis", "function": "__init__", "line": 30, "thread": 131814122619008, "thread_name": "MainThread", "process": 440393, "taskName": null, "uptime": 6.079673767089844e-05, "memory_usage": 24.14, "asctime": "2025-08-21 22:16:54"}
{"timestamp": "2025-08-21T22:39:29.309162", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: config/app.yaml (format: yaml, priority: 1)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 135327515086976, "thread_name": "MainThread", "process": 441995, "taskName": null, "uptime": 0.019799470901489258, "memory_usage": 23.89, "asctime": "2025-08-21 22:39:29"}
{"timestamp": "2025-08-21T22:39:29.315636", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: config/models.yaml (format: yaml, priority: 2)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 135327515086976, "thread_name": "MainThread", "process": 441995, "taskName": null, "uptime": 0.026263952255249023, "memory_usage": 23.89, "asctime": "2025-08-21 22:39:29"}
{"timestamp": "2025-08-21T22:39:29.315899", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Added configuration source: config/logging.yaml (format: yaml, priority: 3)", "module": "config_loader", "function": "add_source", "line": 129, "thread": 135327515086976, "thread_name": "MainThread", "process": 441995, "taskName": null, "uptime": 0.02651524543762207, "memory_usage": 23.89, "asctime": "2025-08-21 22:39:29"}
{"timestamp": "2025-08-21T22:39:29.316157", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.config", "message": "Configuration reloaded successfully", "module": "config_loader", "function": "reload_all", "line": 240, "thread": 135327515086976, "thread_name": "MainThread", "process": 441995, "taskName": null, "uptime": 0.02677321434020996, "memory_usage": 23.89, "asctime": "2025-08-21 22:39:29"}
{"timestamp": "2025-08-21T22:39:29.316391", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: simple_echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 135327515086976, "thread_name": "MainThread", "process": 441995, "taskName": null, "uptime": 0.027007341384887695, "memory_usage": 23.89, "asctime": "2025-08-21 22:39:29"}
{"timestamp": "2025-08-21T22:39:29.316547", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: simple_echo", "module": "jarvis", "function": "__init__", "line": 16, "thread": 135327515086976, "thread_name": "MainThread", "process": 441995, "taskName": null, "uptime": 0.027163982391357422, "memory_usage": 23.89, "asctime": "2025-08-21 22:39:29"}
{"timestamp": "2025-08-21T22:39:29.316716", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 135327515086976, "thread_name": "MainThread", "process": 441995, "taskName": null, "uptime": 0.027331829071044922, "memory_usage": 23.89, "asctime": "2025-08-21 22:39:29"}
{"timestamp": "2025-08-21T22:39:29.317664", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: No module named 'plugins.core.plugin_manager'", "module": "jarvis", "function": "__init__", "line": 30, "thread": 135327515086976, "thread_name": "MainThread", "process": 441995, "taskName": null, "uptime": 0.02828073501586914, "memory_usage": 24.02, "asctime": "2025-08-21 22:39:29"}
{"timestamp": "2025-08-21T22:44:46.569013", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: openhermes-2.5-mistral-7b.Q4_K_M.gguf", "module": "base_model", "function": "__init__", "line": 107, "thread": 138133811331200, "thread_name": "MainThread", "process": 442397, "taskName": null, "uptime": 0.007237911224365234, "memory_usage": 22.19, "asctime": "2025-08-21 22:44:46"}
{"timestamp": "2025-08-21T22:44:46.613592", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: openhermes-2.5-mistral-7b.Q4_K_M.gguf", "module": "jarvis", "function": "__init__", "line": 16, "thread": 138133811331200, "thread_name": "MainThread", "process": 442397, "taskName": null, "uptime": 0.051801443099975586, "memory_usage": 22.19, "asctime": "2025-08-21 22:44:46"}
{"timestamp": "2025-08-21T22:44:46.614136", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 138133811331200, "thread_name": "MainThread", "process": 442397, "taskName": null, "uptime": 0.05231785774230957, "memory_usage": 22.19, "asctime": "2025-08-21 22:44:46"}
{"timestamp": "2025-08-21T22:44:46.615415", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: No module named 'plugins.core.plugin_manager'", "module": "jarvis", "function": "__init__", "line": 30, "thread": 138133811331200, "thread_name": "MainThread", "process": 442397, "taskName": null, "uptime": 0.05359053611755371, "memory_usage": 22.19, "asctime": "2025-08-21 22:44:46"}
{"timestamp": "2025-08-22T00:07:57.382787", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: openhermes-2.5-mistral-7b.Q4_K_M.gguf", "module": "base_model", "function": "__init__", "line": 107, "thread": 140182931832960, "thread_name": "MainThread", "process": 445829, "taskName": null, "uptime": 0.008294343948364258, "memory_usage": 22.1, "asctime": "2025-08-22 00:07:57"}
{"timestamp": "2025-08-22T00:07:57.398215", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: openhermes-2.5-mistral-7b.Q4_K_M.gguf", "module": "jarvis", "function": "__init__", "line": 16, "thread": 140182931832960, "thread_name": "MainThread", "process": 445829, "taskName": null, "uptime": 0.023705482482910156, "memory_usage": 22.22, "asctime": "2025-08-22 00:07:57"}
{"timestamp": "2025-08-22T00:07:57.398550", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 140182931832960, "thread_name": "MainThread", "process": 445829, "taskName": null, "uptime": 0.024025440216064453, "memory_usage": 22.22, "asctime": "2025-08-22 00:07:57"}
{"timestamp": "2025-08-22T00:07:57.399667", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: No module named 'plugins.core.plugin_manager'", "module": "jarvis", "function": "__init__", "line": 30, "thread": 140182931832960, "thread_name": "MainThread", "process": 445829, "taskName": null, "uptime": 0.025136470794677734, "memory_usage": 22.22, "asctime": "2025-08-22 00:07:57"}
{"timestamp": "2025-08-22T00:07:57.644724", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Static files mounted from: web/static", "module": "web_interface", "function": "setup_static_files", "line": 165, "thread": 140182931832960, "thread_name": "MainThread", "process": 445829, "taskName": null, "uptime": 0.27020716667175293, "memory_usage": 45.2, "asctime": "2025-08-22 00:07:57"}
{"timestamp": "2025-08-22T00:07:57.645072", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Templates loaded from: web/templates", "module": "web_interface", "function": "setup_static_files", "line": 181, "thread": 140182931832960, "thread_name": "MainThread", "process": 445829, "taskName": null, "uptime": 0.27054667472839355, "memory_usage": 45.2, "asctime": "2025-08-22 00:07:57"}
{"timestamp": "2025-08-22T00:07:57.662608", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Starting web interface on 127.0.0.1:8080", "module": "web_interface", "function": "start", "line": 786, "thread": 140182931832960, "thread_name": "MainThread", "process": 445829, "taskName": null, "uptime": 0.28809261322021484, "memory_usage": 47.66, "asctime": "2025-08-22 00:07:57"}
{"timestamp": "2025-08-22T00:07:57.679726", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.web", "message": "Jarvis Web Interface starting up...", "module": "web_interface", "function": "startup_event", "line": 759, "thread": 140182931832960, "thread_name": "MainThread", "process": 445829, "taskName": "Task-2", "uptime": 0.30519914627075195, "memory_usage": 48.79, "asctime": "2025-08-22 00:07:57"}
{"timestamp": "2025-08-22T00:08:11.417169", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis.web", "message": "Internal server error: No route exists for name \"static\" and params \"filename\".", "module": "web_interface", "function": "internal_error_handler", "line": 746, "thread": 140182931832960, "thread_name": "MainThread", "process": 445829, "taskName": "Task-5", "uptime": 14.042662382125854, "memory_usage": 49.71, "asctime": "2025-08-22 00:08:11"}
{"timestamp": "2025-08-22T00:08:36.709501", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis.web", "message": "Internal server error: No route exists for name \"static\" and params \"filename\".", "module": "web_interface", "function": "internal_error_handler", "line": 746, "thread": 140182931832960, "thread_name": "MainThread", "process": 445829, "taskName": "Task-9", "uptime": 39.33501076698303, "memory_usage": 50.71, "asctime": "2025-08-22 00:08:36"}
{"timestamp": "2025-08-22T00:08:48.082384", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: openhermes-2.5-mistral-7b.Q4_K_M.gguf", "module": "base_model", "function": "__init__", "line": 107, "thread": 127065822363776, "thread_name": "MainThread", "process": 446264, "taskName": null, "uptime": 0.012226581573486328, "memory_usage": 22.04, "asctime": "2025-08-22 00:08:48"}
{"timestamp": "2025-08-22T00:08:48.098723", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: openhermes-2.5-mistral-7b.Q4_K_M.gguf", "module": "jarvis", "function": "__init__", "line": 16, "thread": 127065822363776, "thread_name": "MainThread", "process": 446264, "taskName": null, "uptime": 0.028563499450683594, "memory_usage": 22.17, "asctime": "2025-08-22 00:08:48"}
{"timestamp": "2025-08-22T00:08:48.099152", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 127065822363776, "thread_name": "MainThread", "process": 446264, "taskName": null, "uptime": 0.02895188331604004, "memory_usage": 22.17, "asctime": "2025-08-22 00:08:48"}
{"timestamp": "2025-08-22T00:08:48.100192", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: No module named 'plugins.core.plugin_manager'", "module": "jarvis", "function": "__init__", "line": 30, "thread": 127065822363776, "thread_name": "MainThread", "process": 446264, "taskName": null, "uptime": 0.02998828887939453, "memory_usage": 22.17, "asctime": "2025-08-22 00:08:48"}
{"timestamp": "2025-08-22T00:08:48.318620", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis.error", "message": "[ERR_1755839328_5001] SpeechRecognizer.__init__() got an unexpected keyword argument 'language' | Context: {\"component\": \"voice_interface_init\"}", "module": "error_handler", "function": "_log_error", "line": 184, "thread": 127065822363776, "thread_name": "MainThread", "process": 446264, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/jarviscelviteaz/src/interfaces/voice/voice_interface.py\", line 90, in _initialize_components\n    self.speech_recognizer = SpeechRecognizer(\n                             ^^^^^^^^^^^^^^^^^\nTypeError: SpeechRecognizer.__init__() got an unexpected keyword argument 'language'", "taskName": null, "uptime": 0.24843144416809082, "memory_usage": 43.38, "asctime": "2025-08-22 00:08:48"}
{"timestamp": "2025-08-22T00:08:48.319158", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Failed to initialize voice components: SpeechRecognizer.__init__() got an unexpected keyword argument 'language'", "module": "voice_interface", "function": "_initialize_components", "line": 106, "thread": 127065822363776, "thread_name": "MainThread", "process": 446264, "taskName": null, "uptime": 0.24895858764648438, "memory_usage": 43.38, "asctime": "2025-08-22 00:08:48"}
{"timestamp": "2025-08-22T00:08:48.319473", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Voice speaking worker started", "module": "voice_interface", "function": "_speak_worker", "line": 189, "thread": 127065788135104, "thread_name": "Thread-1 (_speak_worker)", "process": 446264, "taskName": null, "uptime": 0.24927186965942383, "memory_usage": 43.38, "asctime": "2025-08-22 00:08:48"}
{"timestamp": "2025-08-22T00:08:48.319594", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Voice listening worker started", "module": "voice_interface", "function": "_listen_worker", "line": 156, "thread": 127065779742400, "thread_name": "Thread-2 (_listen_worker)", "process": 446264, "taskName": null, "uptime": 0.24939560890197754, "memory_usage": 43.38, "asctime": "2025-08-22 00:08:48"}
{"timestamp": "2025-08-22T00:08:48.320038", "level": "DEBUG", "logger": "jarvis.interfaces.voice", "message": "Voice state changed: idle -> speaking", "module": "voice_interface", "function": "_set_state", "line": 308, "thread": 127065788135104, "thread_name": "Thread-1 (_speak_worker)", "process": 446264, "taskName": null, "asctime": "2025-08-22 00:08:48"}
{"timestamp": "2025-08-22T00:08:48.319735", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Voice interface started successfully", "module": "voice_interface", "function": "start", "line": 131, "thread": 127065822363776, "thread_name": "MainThread", "process": 446264, "taskName": null, "uptime": 0.24953174591064453, "memory_usage": 43.38, "asctime": "2025-08-22 00:08:48"}
{"timestamp": "2025-08-22T00:09:34.475611", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: openhermes-2.5-mistral-7b.Q4_K_M.gguf", "module": "base_model", "function": "__init__", "line": 107, "thread": 135152220184704, "thread_name": "MainThread", "process": 446314, "taskName": null, "uptime": 0.010598182678222656, "memory_usage": 22.05, "asctime": "2025-08-22 00:09:34"}
{"timestamp": "2025-08-22T00:09:34.490053", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: openhermes-2.5-mistral-7b.Q4_K_M.gguf", "module": "jarvis", "function": "__init__", "line": 16, "thread": 135152220184704, "thread_name": "MainThread", "process": 446314, "taskName": null, "uptime": 0.025021791458129883, "memory_usage": 22.17, "asctime": "2025-08-22 00:09:34"}
{"timestamp": "2025-08-22T00:09:34.490463", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 135152220184704, "thread_name": "MainThread", "process": 446314, "taskName": null, "uptime": 0.025417804718017578, "memory_usage": 22.17, "asctime": "2025-08-22 00:09:34"}
{"timestamp": "2025-08-22T00:09:34.491390", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: No module named 'plugins.core.plugin_manager'", "module": "jarvis", "function": "__init__", "line": 30, "thread": 135152220184704, "thread_name": "MainThread", "process": 446314, "taskName": null, "uptime": 0.026340961456298828, "memory_usage": 22.17, "asctime": "2025-08-22 00:09:34"}
{"timestamp": "2025-08-22T00:10:17.968271", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: openhermes-2.5-mistral-7b.Q4_K_M.gguf", "module": "base_model", "function": "__init__", "line": 107, "thread": 140563535052928, "thread_name": "MainThread", "process": 446447, "taskName": null, "uptime": 0.00819706916809082, "memory_usage": 21.66, "asctime": "2025-08-22 00:10:17"}
{"timestamp": "2025-08-22T00:10:17.988120", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: openhermes-2.5-mistral-7b.Q4_K_M.gguf", "module": "jarvis", "function": "__init__", "line": 16, "thread": 140563535052928, "thread_name": "MainThread", "process": 446447, "taskName": null, "uptime": 0.028040647506713867, "memory_usage": 21.79, "asctime": "2025-08-22 00:10:17"}
{"timestamp": "2025-08-22T00:10:17.988633", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 140563535052928, "thread_name": "MainThread", "process": 446447, "taskName": null, "uptime": 0.028525114059448242, "memory_usage": 21.79, "asctime": "2025-08-22 00:10:17"}
{"timestamp": "2025-08-22T00:10:17.990133", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: No module named 'plugins.core.plugin_manager'", "module": "jarvis", "function": "__init__", "line": 30, "thread": 140563535052928, "thread_name": "MainThread", "process": 446447, "taskName": null, "uptime": 0.030019283294677734, "memory_usage": 21.79, "asctime": "2025-08-22 00:10:17"}
{"timestamp": "2025-08-22T00:10:18.311150", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis.interfaces.voice.speech_recognition", "message": "speech_recognition library not installed", "module": "speech_recognition", "function": "_initialize_components", "line": 85, "thread": 140563535052928, "thread_name": "MainThread", "process": 446447, "taskName": null, "uptime": 0.35104990005493164, "memory_usage": 44.03, "asctime": "2025-08-22 00:10:18"}
{"timestamp": "2025-08-22T00:10:18.312611", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis.error", "message": "[ERR_1755839418_2800] No module named 'speech_recognition' | Context: {\"component\": \"voice_interface_init\"}", "module": "error_handler", "function": "_log_error", "line": 184, "thread": 140563535052928, "thread_name": "MainThread", "process": 446447, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/jarviscelviteaz/src/interfaces/voice/voice_interface.py\", line 90, in _initialize_components\n    self.speech_recognizer = SpeechRecognizer()\n                             ^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/Desktop/jarviscelviteaz/src/interfaces/voice/speech_recognition.py\", line 59, in __init__\n    self._initialize_components()\n  File \"/home/<USER>/Desktop/jarviscelviteaz/src/interfaces/voice/speech_recognition.py\", line 64, in _initialize_components\n    import speech_recognition as sr\nModuleNotFoundError: No module named 'speech_recognition'", "taskName": null, "uptime": 0.35251569747924805, "memory_usage": 44.03, "asctime": "2025-08-22 00:10:18"}
{"timestamp": "2025-08-22T00:10:18.313766", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Failed to initialize voice components: No module named 'speech_recognition'", "module": "voice_interface", "function": "_initialize_components", "line": 103, "thread": 140563535052928, "thread_name": "MainThread", "process": 446447, "taskName": null, "uptime": 0.3536567687988281, "memory_usage": 44.03, "asctime": "2025-08-22 00:10:18"}
{"timestamp": "2025-08-22T00:10:18.314899", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Voice speaking worker started", "module": "voice_interface", "function": "_speak_worker", "line": 186, "thread": 140563500906176, "thread_name": "Thread-1 (_speak_worker)", "process": 446447, "taskName": null, "uptime": 0.35479092597961426, "memory_usage": 44.03, "asctime": "2025-08-22 00:10:18"}
{"timestamp": "2025-08-22T00:10:18.318091", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Voice interface started successfully", "module": "voice_interface", "function": "start", "line": 128, "thread": 140563535052928, "thread_name": "MainThread", "process": 446447, "taskName": null, "uptime": 0.35797548294067383, "memory_usage": 44.03, "asctime": "2025-08-22 00:10:18"}
{"timestamp": "2025-08-22T00:10:18.318284", "level": "DEBUG", "logger": "jarvis.interfaces.voice", "message": "Voice state changed: idle -> speaking", "module": "voice_interface", "function": "_set_state", "line": 305, "thread": 140563500906176, "thread_name": "Thread-1 (_speak_worker)", "process": 446447, "taskName": null, "asctime": "2025-08-22 00:10:18"}
{"timestamp": "2025-08-22T00:11:44.256666", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: openhermes-2.5-mistral-7b.Q4_K_M.gguf", "module": "base_model", "function": "__init__", "line": 107, "thread": 124682524205184, "thread_name": "MainThread", "process": 446750, "taskName": null, "uptime": 0.006855964660644531, "memory_usage": 22.05, "asctime": "2025-08-22 00:11:44"}
{"timestamp": "2025-08-22T00:11:44.270252", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.core", "message": "Initialized Jarvis with model: openhermes-2.5-mistral-7b.Q4_K_M.gguf", "module": "jarvis", "function": "__init__", "line": 16, "thread": 124682524205184, "thread_name": "MainThread", "process": 446750, "taskName": null, "uptime": 0.02042984962463379, "memory_usage": 22.17, "asctime": "2025-08-22 00:11:44"}
{"timestamp": "2025-08-22T00:11:44.270543", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.models.base", "message": "Initialized model: echo", "module": "base_model", "function": "__init__", "line": 107, "thread": 124682524205184, "thread_name": "MainThread", "process": 446750, "taskName": null, "uptime": 0.02070903778076172, "memory_usage": 22.17, "asctime": "2025-08-22 00:11:44"}
{"timestamp": "2025-08-22T00:11:44.271412", "level": "\u001b[33mWARNING\u001b[0m", "logger": "jarvis.core", "message": "Plugin system unavailable: No module named 'plugins.core.plugin_manager'", "module": "jarvis", "function": "__init__", "line": 30, "thread": 124682524205184, "thread_name": "MainThread", "process": 446750, "taskName": null, "uptime": 0.021575927734375, "memory_usage": 22.17, "asctime": "2025-08-22 00:11:44"}
{"timestamp": "2025-08-22T00:11:45.750127", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.interfaces.voice.speech_recognition", "message": "Adjusting for ambient noise...", "module": "speech_recognition", "function": "_initialize_components", "line": 79, "thread": 124682524205184, "thread_name": "MainThread", "process": 446750, "taskName": null, "uptime": 1.5003151893615723, "memory_usage": 51.62, "asctime": "2025-08-22 00:11:45"}
{"timestamp": "2025-08-22T00:11:46.894131", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.interfaces.voice.speech_recognition", "message": "Speech recognition initialized with google engine", "module": "speech_recognition", "function": "_initialize_components", "line": 82, "thread": 124682524205184, "thread_name": "MainThread", "process": 446750, "taskName": null, "uptime": 2.644317150115967, "memory_usage": 50.66, "asctime": "2025-08-22 00:11:46"}
{"timestamp": "2025-08-22T00:11:46.894853", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis.error", "message": "[ERR_1755839506_5398] TextToSpeech.__init__() got an unexpected keyword argument 'voice_id' | Context: {\"component\": \"voice_interface_init\"}", "module": "error_handler", "function": "_log_error", "line": 184, "thread": 124682524205184, "thread_name": "MainThread", "process": 446750, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/jarviscelviteaz/src/interfaces/voice/voice_interface.py\", line 93, in _initialize_components\n    self.text_to_speech = TextToSpeech(\n                          ^^^^^^^^^^^^^\nTypeError: TextToSpeech.__init__() got an unexpected keyword argument 'voice_id'", "taskName": null, "uptime": 2.645021677017212, "memory_usage": 50.66, "asctime": "2025-08-22 00:11:46"}
{"timestamp": "2025-08-22T00:11:46.895241", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Failed to initialize voice components: TextToSpeech.__init__() got an unexpected keyword argument 'voice_id'", "module": "voice_interface", "function": "_initialize_components", "line": 103, "thread": 124682524205184, "thread_name": "MainThread", "process": 446750, "taskName": null, "uptime": 2.6454098224639893, "memory_usage": 50.66, "asctime": "2025-08-22 00:11:46"}
{"timestamp": "2025-08-22T00:11:46.895585", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Voice speaking worker started", "module": "voice_interface", "function": "_speak_worker", "line": 186, "thread": 124682477823680, "thread_name": "Thread-1 (_speak_worker)", "process": 446750, "taskName": null, "uptime": 2.645754814147949, "memory_usage": 50.66, "asctime": "2025-08-22 00:11:46"}
{"timestamp": "2025-08-22T00:11:46.895724", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Voice listening worker started", "module": "voice_interface", "function": "_listen_worker", "line": 153, "thread": 124682467804864, "thread_name": "Thread-2 (_listen_worker)", "process": 446750, "taskName": null, "uptime": 2.645894765853882, "memory_usage": 50.66, "asctime": "2025-08-22 00:11:46"}
{"timestamp": "2025-08-22T00:11:46.896145", "level": "DEBUG", "logger": "jarvis.interfaces.voice", "message": "Voice state changed: idle -> speaking", "module": "voice_interface", "function": "_set_state", "line": 305, "thread": 124682477823680, "thread_name": "Thread-1 (_speak_worker)", "process": 446750, "taskName": null, "asctime": "2025-08-22 00:11:46"}
{"timestamp": "2025-08-22T00:11:46.895813", "level": "\u001b[32mINFO\u001b[0m", "logger": "jarvis.interfaces.voice", "message": "Voice interface started successfully", "module": "voice_interface", "function": "start", "line": 128, "thread": 124682524205184, "thread_name": "MainThread", "process": 446750, "taskName": null, "uptime": 2.6459829807281494, "memory_usage": 50.66, "asctime": "2025-08-22 00:11:46"}
{"timestamp": "2025-08-22T00:12:10.731626", "level": "\u001b[31mERROR\u001b[0m", "logger": "jarvis.web", "message": "Internal server error: No route exists for name \"static\" and params \"filename\".", "module": "web_interface", "function": "internal_error_handler", "line": 746, "thread": 140182931832960, "thread_name": "MainThread", "process": 445829, "taskName": "Task-13", "uptime": 253.35713458061218, "memory_usage": 50.71, "asctime": "2025-08-22 00:12:10"}
