#!/usr/bin/env python3
"""
Jarvis AI Assistant - Main Runner
Entry point that handles imports and starts the application.
"""

import sys
import os
import argparse
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Jarvis AI Assistant")
    parser.add_argument("--interface", "-i", choices=["cli", "web", "voice"], 
                       default="cli", help="Interface to use")
    parser.add_argument("--host", default="127.0.0.1", help="Host for web interface")
    parser.add_argument("--port", type=int, default=8000, help="Port for web interface")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--config", help="Config file path")
    
    args = parser.parse_args()
    
    print("🤖 Starting Jarvis AI Assistant...")
    print(f"   Interface: {args.interface}")
    print(f"   Debug: {args.debug}")
    
    try:
        # Initialize Jarvis with a real model
        print("🔧 Initializing AI model...")
        from models.discovery import discover_models, pick_preferred
        from core.jarvis import Jarvis

        # Discover available models
        models = discover_models()
        if not models:
            print("❌ No AI models found! Creating a simple echo model for testing...")
            from models.base_model import BaseModel

            class SimpleEchoModel(BaseModel):
                def __init__(self):
                    super().__init__("simple_echo")

                def generate(self, prompt: str, max_tokens: int = 128, temperature: float = 0.0) -> str:
                    return f"Echo response: {prompt}"

            model = SimpleEchoModel()
        else:
            # Pick the best available model
            model = pick_preferred(models)

        print(f"✅ Using model: {model.name}")

        # Initialize Jarvis
        jarvis_app = Jarvis(model)
        print("✅ Jarvis AI initialized successfully!")

        if args.interface == "cli":
            from interfaces.cli.cli_interface import CLIInterface
            interface = CLIInterface(app=jarvis_app)
            interface.start()

        elif args.interface == "web":
            from interfaces.web.web_interface import WebInterface, init as web_init
            # Initialize web interface with Jarvis
            web_init(jarvis_app)
            interface = WebInterface(host=args.host, port=args.port, debug=args.debug)
            print(f"   Starting web server on http://{args.host}:{args.port}")
            interface.start()

        elif args.interface == "voice":
            from interfaces.voice.voice_interface import VoiceInterface
            interface = VoiceInterface(app=jarvis_app)
            interface.start()
            
    except KeyboardInterrupt:
        print("\n👋 Jarvis AI Assistant stopped by user")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure all dependencies are installed")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting Jarvis: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
